import cv2
import sys

def test_camera():
    print("Testing camera access...")
    
    # Try different camera indices
    for i in range(5):
        print(f"\nTrying camera index {i}...")
        cap = cv2.VideoCapture(i)
        
        if cap.isOpened():
            print(f"Camera {i} opened successfully!")
            
            # Try to read a frame
            ret, frame = cap.read()
            if ret and frame is not None:
                print(f"Camera {i} can read frames successfully!")
                print(f"Frame shape: {frame.shape}")
                
                # Show the frame for 3 seconds
                cv2.imshow(f'Camera {i} Test', frame)
                cv2.waitKey(3000)
                cv2.destroyAllWindows()
                
                cap.release()
                return i
            else:
                print(f"Camera {i} opened but cannot read frames")
        else:
            print(f"Camera {i} could not be opened")
        
        cap.release()
    
    print("\nNo working camera found!")
    return None

if __name__ == "__main__":
    working_camera = test_camera()
    if working_camera is not None:
        print(f"\nWorking camera found at index: {working_camera}")
        print("You can use this index in your volume_project.py")
    else:
        print("\nTroubleshooting tips:")
        print("1. Make sure no other applications are using the camera")
        print("2. Check Windows camera privacy settings")
        print("3. Try running as administrator")
        print("4. Check if camera drivers are installed properly")
